#!/usr/bin/env python3
"""
Test script for the Secure Login System
"""

import sys
import os
import unittest
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import app
    from models import db, LoginAttempt, FailedAttemptTracker
    from auth import AuthManager
    from utils import get_device_info, parse_user_agent_manual
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're in the login directory and dependencies are installed")
    sys.exit(1)

class TestLoginSystem(unittest.TestCase):
    """Test cases for the login system"""
    
    def setUp(self):
        """Set up test environment"""
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        self.app = app.test_client()
        self.app_context = app.app_context()
        self.app_context.push()
        db.create_all()
    
    def tearDown(self):
        """Clean up after tests"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_database_models(self):
        """Test database models"""
        print("🧪 Testing database models...")
        
        # Test LoginAttempt model
        attempt = LoginAttempt(
            username='test_user',
            success=True,
            ip_address='127.0.0.1',
            device_type='Desktop',
            browser_name='Chrome',
            user_agent='Test User Agent'
        )
        db.session.add(attempt)
        db.session.commit()
        
        # Verify the attempt was saved
        saved_attempt = LoginAttempt.query.first()
        self.assertIsNotNone(saved_attempt)
        self.assertEqual(saved_attempt.username, 'test_user')
        self.assertTrue(saved_attempt.success)
        
        print("✅ Database models working correctly")
    
    def test_authentication(self):
        """Test authentication logic"""
        print("🧪 Testing authentication...")
        
        # Test correct credentials
        self.assertTrue(AuthManager.verify_credentials('admin', 'Admin@123'))
        
        # Test incorrect credentials
        self.assertFalse(AuthManager.verify_credentials('admin', 'wrong_password'))
        self.assertFalse(AuthManager.verify_credentials('wrong_user', 'Admin@123'))
        
        print("✅ Authentication working correctly")
    
    def test_user_agent_parsing(self):
        """Test user agent parsing"""
        print("🧪 Testing user agent parsing...")
        
        test_ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        
        device_info = parse_user_agent_manual(test_ua)
        
        self.assertIsInstance(device_info, dict)
        self.assertIn('device_type', device_info)
        self.assertIn('browser_name', device_info)
        self.assertIn('operating_system', device_info)
        
        print("✅ User agent parsing working correctly")
    
    def test_failed_attempt_tracking(self):
        """Test failed attempt tracking"""
        print("🧪 Testing failed attempt tracking...")
        
        # Create a failed attempt tracker
        tracker = FailedAttemptTracker(ip_address='***********')
        db.session.add(tracker)
        db.session.commit()
        
        # Test incrementing failed attempts
        tracker.increment_failed_attempts()
        self.assertEqual(tracker.failed_count, 1)
        self.assertFalse(tracker.is_locked())
        
        # Test lockout after 5 attempts
        for _ in range(4):
            tracker.increment_failed_attempts()
        
        self.assertEqual(tracker.failed_count, 5)
        self.assertTrue(tracker.is_locked())
        
        print("✅ Failed attempt tracking working correctly")
    
    def test_routes(self):
        """Test basic routes"""
        print("🧪 Testing routes...")
        
        # Test index route (should redirect to login)
        response = self.app.get('/')
        self.assertEqual(response.status_code, 302)
        
        # Test login route
        response = self.app.get('/login')
        self.assertEqual(response.status_code, 200)
        
        print("✅ Routes working correctly")

def run_system_check():
    """Run system compatibility check"""
    print("🔍 SYSTEM COMPATIBILITY CHECK")
    print("=" * 50)
    
    # Check Python version
    python_version = sys.version_info
    print(f"Python Version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    else:
        print("✅ Python version compatible")
    
    # Check required modules
    required_modules = [
        'flask', 'flask_sqlalchemy', 'requests', 'user_agents'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module.replace('_', '.'))
            print(f"✅ {module} available")
        except ImportError:
            print(f"❌ {module} missing")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n📦 Install missing modules with:")
        print(f"pip install {' '.join(missing_modules)}")
        return False
    
    # Check file structure
    required_files = [
        'app.py', 'models.py', 'auth.py', 'utils.py', 'config.py',
        'templates/login.html', 'templates/dashboard.html',
        'static/css/login.css', 'static/css/dashboard.css'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path} exists")
    
    if missing_files:
        print(f"\n❌ Missing files: {', '.join(missing_files)}")
        return False
    
    print("\n🎉 System check passed! Ready to run the login system.")
    return True

def main():
    """Main function"""
    print("🔐 SECURE LOGIN SYSTEM - TEST SUITE")
    print("=" * 60)
    print()
    
    # Run system check first
    if not run_system_check():
        print("\n❌ System check failed. Please fix the issues above.")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🧪 RUNNING UNIT TESTS")
    print("=" * 50)
    
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=0)
    
    print("\n" + "=" * 50)
    print("✅ ALL TESTS COMPLETED")
    print("=" * 50)
    print()
    print("🚀 You can now start the login system with:")
    print("   python run.py")
    print("   or")
    print("   ./start.sh (Linux/macOS)")
    print("   or")
    print("   start.bat (Windows)")

if __name__ == '__main__':
    main()
