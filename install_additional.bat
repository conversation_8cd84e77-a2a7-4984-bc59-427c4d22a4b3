@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🚀 تثبيت المكتبات الإضافية
echo ========================================
echo.

echo 📦 تفعيل البيئة الافتراضية...
call web_env\Scripts\activate.bat

echo.
echo 📦 تثبيت مكتبات التطوير الأساسية...
python -m pip install --upgrade pip
python -m pip install tornado httpx aiohttp pandas numpy openpyxl bcrypt pytest black flake8

echo.
echo 📦 تثبيت مكتبات التاريخ والوقت...
python -m pip install python-dateutil pytz

echo.
echo 📦 تثبيت مكتبات معالجة الصور والملفات...
python -m pip install Pillow

echo.
echo 📦 تثبيت مكتبات الويب سكرابينغ...
python -m pip install beautifulsoup4 lxml

echo.
echo 📦 تثبيت مكتبات الرسوم البيانية...
python -m pip install matplotlib seaborn plotly

echo.
echo 📦 تثبيت أدوات إضافية...
python -m pip install tqdm ipython

echo.
echo ✅ تم تثبيت المكتبات الإضافية بنجاح!
echo.
echo 🧪 تشغيل اختبار شامل...
python test_installation.py

echo.
echo 🎉 انتهى التثبيت!
echo 💡 يمكنك الآن تشغيل: python test_app.py
pause
