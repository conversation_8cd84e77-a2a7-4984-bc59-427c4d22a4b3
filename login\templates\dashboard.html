<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Login Monitoring</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/dashboard.css') }}" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-shield-alt"></i> Admin Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i> {{ session.username }}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Total Attempts</h5>
                                <h3>{{ stats.total_attempts }}</h3>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Successful</h5>
                                <h3>{{ stats.successful_attempts }}</h3>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Failed</h5>
                                <h3>{{ stats.failed_attempts }}</h3>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-times-circle"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Locked IPs</h5>
                                <h3>{{ stats.locked_ips }}</h3>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-lock"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-filter"></i> Filters</h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="status_filter" class="form-label">Status</label>
                        <select class="form-select" id="status_filter" name="status">
                            <option value="">All</option>
                            <option value="success" {% if request.args.get('status') == 'success' %}selected{% endif %}>Success</option>
                            <option value="failed" {% if request.args.get('status') == 'failed' %}selected{% endif %}>Failed</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="date_from" class="form-label">From Date</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request.args.get('date_from', '') }}">
                    </div>
                    <div class="col-md-3">
                        <label for="date_to" class="form-label">To Date</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request.args.get('date_to', '') }}">
                    </div>
                    <div class="col-md-3">
                        <label for="ip_filter" class="form-label">IP Address</label>
                        <input type="text" class="form-control" id="ip_filter" name="ip" placeholder="Filter by IP" value="{{ request.args.get('ip', '') }}">
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Apply Filters
                        </button>
                        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                        <button type="button" class="btn btn-success" onclick="exportData()">
                            <i class="fas fa-download"></i> Export CSV
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Login Attempts Table -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list"></i> Login Attempts Log</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="loginAttemptsTable" class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Timestamp</th>
                                <th>Username</th>
                                <th>Status</th>
                                <th>IP Address</th>
                                <th>Location</th>
                                <th>Device</th>
                                <th>Browser</th>
                                <th>OS</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for attempt in attempts %}
                            <tr>
                                <td>{{ attempt.timestamp.strftime('%Y-%m-%d %H:%M:%S') if attempt.timestamp else 'Unknown' }}</td>
                                <td>{{ attempt.username }}</td>
                                <td>
                                    {% if attempt.success %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> Success
                                        </span>
                                    {% else %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times"></i> Failed
                                        </span>
                                    {% endif %}
                                </td>
                                <td>{{ attempt.ip_address }}</td>
                                <td>
                                    {% if attempt.location_city and attempt.location_country %}
                                        {{ attempt.location_city }}, {{ attempt.location_country }}
                                    {% else %}
                                        {{ attempt.location_country or 'Unknown' }}
                                    {% endif %}
                                </td>
                                <td>{{ attempt.device_type or 'Unknown' }}</td>
                                <td>{{ attempt.browser_name or 'Unknown' }} {{ attempt.browser_version or '' }}</td>
                                <td>{{ attempt.operating_system or 'Unknown' }}</td>
                                <td>
                                    <button class="btn btn-sm btn-info" onclick="showDetails({{ attempt.id }})">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Details Modal -->
    <div class="modal fade" id="detailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Login Attempt Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- Details will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
</body>
</html>
