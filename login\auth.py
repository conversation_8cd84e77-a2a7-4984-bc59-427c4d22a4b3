from flask import session, request, flash, redirect, url_for
from datetime import datetime, timedelta
from models import db, LoginAttempt, FailedAttemptTracker
from utils import get_device_info, get_location_info
import hashlib
import secrets

# Hardcoded credentials
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "Admin@123"

class AuthManager:
    """Handles authentication logic and security features"""
    
    @staticmethod
    def verify_credentials(username, password):
        """Verify username and password against hardcoded credentials"""
        return username == ADMIN_USERNAME and password == ADMIN_PASSWORD
    
    @staticmethod
    def get_client_ip():
        """Get client IP address, handling proxies"""
        if request.environ.get('HTTP_X_FORWARDED_FOR') is None:
            return request.environ['REMOTE_ADDR']
        else:
            # Handle multiple IPs in X-Forwarded-For header
            return request.environ['HTTP_X_FORWARDED_FOR'].split(',')[0].strip()
    
    @staticmethod
    def is_ip_locked(ip_address):
        """Check if IP address is currently locked"""
        tracker = FailedAttemptTracker.query.filter_by(ip_address=ip_address).first()
        if tracker:
            return tracker.is_locked()
        return False
    
    @staticmethod
    def get_lockout_time_remaining(ip_address):
        """Get remaining lockout time for IP address"""
        tracker = FailedAttemptTracker.query.filter_by(ip_address=ip_address).first()
        if tracker:
            return tracker.get_remaining_lockout_time()
        return 0
    
    @staticmethod
    def record_login_attempt(username, success, ip_address, user_agent):
        """Record login attempt in database with all tracking information"""
        try:
            # Get device and browser information
            device_info = get_device_info(user_agent)
            
            # Get location information
            location_info = get_location_info(ip_address)
            
            # Create login attempt record
            attempt = LoginAttempt(
                username=username,
                timestamp=datetime.utcnow(),
                success=success,
                ip_address=ip_address,
                device_type=device_info.get('device_type'),
                browser_name=device_info.get('browser_name'),
                browser_version=device_info.get('browser_version'),
                operating_system=device_info.get('operating_system'),
                user_agent=user_agent,
                location_country=location_info.get('country'),
                location_city=location_info.get('city'),
                location_region=location_info.get('region'),
                latitude=location_info.get('latitude'),
                longitude=location_info.get('longitude')
            )
            
            db.session.add(attempt)
            db.session.commit()
            
        except Exception as e:
            print(f"Error recording login attempt: {e}")
            db.session.rollback()
    
    @staticmethod
    def handle_failed_attempt(ip_address):
        """Handle failed login attempt and update lockout tracker"""
        try:
            tracker = FailedAttemptTracker.query.filter_by(ip_address=ip_address).first()
            
            if not tracker:
                tracker = FailedAttemptTracker(ip_address=ip_address)
                db.session.add(tracker)
            
            tracker.increment_failed_attempts()
            db.session.commit()
            
            return tracker.is_locked(), tracker.get_remaining_lockout_time()
            
        except Exception as e:
            print(f"Error handling failed attempt: {e}")
            db.session.rollback()
            return False, 0
    
    @staticmethod
    def handle_successful_login(ip_address):
        """Handle successful login and reset failed attempts"""
        try:
            tracker = FailedAttemptTracker.query.filter_by(ip_address=ip_address).first()
            
            if tracker:
                tracker.reset_attempts()
                db.session.commit()
                
        except Exception as e:
            print(f"Error handling successful login: {e}")
            db.session.rollback()
    
    @staticmethod
    def login_user(username, password):
        """Main login function with all security checks"""
        ip_address = AuthManager.get_client_ip()
        user_agent = request.headers.get('User-Agent', '')
        
        # Check if IP is locked
        if AuthManager.is_ip_locked(ip_address):
            remaining_time = AuthManager.get_lockout_time_remaining(ip_address)
            minutes = remaining_time // 60
            seconds = remaining_time % 60
            flash(f'Too many failed attempts. Please try again in {minutes}m {seconds}s.', 'error')
            return False, 'locked'
        
        # Verify credentials
        if AuthManager.verify_credentials(username, password):
            # Successful login
            session['logged_in'] = True
            session['username'] = username
            session['login_time'] = datetime.utcnow().isoformat()
            
            # Record successful attempt
            AuthManager.record_login_attempt(username, True, ip_address, user_agent)
            
            # Reset failed attempts for this IP
            AuthManager.handle_successful_login(ip_address)
            
            flash('Login successful!', 'success')
            return True, 'success'
        else:
            # Failed login
            AuthManager.record_login_attempt(username, False, ip_address, user_agent)
            
            # Handle failed attempt and check for lockout
            is_locked, remaining_time = AuthManager.handle_failed_attempt(ip_address)
            
            if is_locked:
                minutes = remaining_time // 60
                seconds = remaining_time % 60
                flash(f'Too many failed attempts. Account locked for {minutes}m {seconds}s.', 'error')
                return False, 'locked'
            else:
                flash('Invalid username or password.', 'error')
                return False, 'invalid'
    
    @staticmethod
    def logout_user():
        """Logout user and clear session"""
        session.clear()
        flash('You have been logged out.', 'info')
    
    @staticmethod
    def is_logged_in():
        """Check if user is currently logged in"""
        return session.get('logged_in', False)
    
    @staticmethod
    def require_login():
        """Decorator function to require login for routes"""
        def decorator(f):
            def wrapper(*args, **kwargs):
                if not AuthManager.is_logged_in():
                    flash('Please log in to access this page.', 'error')
                    return redirect(url_for('login'))
                return f(*args, **kwargs)
            wrapper.__name__ = f.__name__
            return wrapper
        return decorator

def generate_csrf_token():
    """Generate CSRF token for forms"""
    if 'csrf_token' not in session:
        session['csrf_token'] = secrets.token_hex(16)
    return session['csrf_token']

def validate_csrf_token(token):
    """Validate CSRF token"""
    return token and session.get('csrf_token') == token
