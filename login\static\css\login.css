/* Login Page Styles */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.login-container {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.login-icon i {
    font-size: 2rem;
    color: white;
}

.login-header h2 {
    color: #333;
    font-weight: 600;
    margin-bottom: 10px;
}

.login-header p {
    color: #666;
    font-size: 0.9rem;
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    font-weight: 500;
    color: #555;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-label i {
    color: #667eea;
    width: 16px;
}

.form-control {
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    padding: 12px 16px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
}

.password-input-group {
    position: relative;
    display: flex;
}

.password-input-group .form-control {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: none;
}

.password-toggle {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: none;
    border: 2px solid #e1e5e9;
    border-left: none;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
}

.password-toggle:hover {
    background: #f8f9fa;
}

.btn-login {
    width: 100%;
    padding: 12px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1.1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-login:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.btn-login:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.flash-messages {
    margin-bottom: 20px;
}

.alert {
    border-radius: 12px;
    border: none;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.lockout-alert {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    color: #2d3436;
    text-align: center;
    margin-bottom: 20px;
}

.lockout-timer {
    font-size: 1.2rem;
    font-weight: bold;
    margin: 10px 0;
}

.login-footer {
    margin-top: 30px;
    text-align: center;
}

.security-info {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 10px;
    padding: 15px;
    display: flex;
    align-items: flex-start;
    gap: 10px;
    text-align: left;
}

.security-info i {
    color: #667eea;
    margin-top: 2px;
}

.security-info small {
    color: #666;
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 480px) {
    .login-card {
        padding: 30px 20px;
        margin: 10px;
        border-radius: 15px;
    }
    
    .login-icon {
        width: 60px;
        height: 60px;
    }
    
    .login-icon i {
        font-size: 1.5rem;
    }
    
    .login-header h2 {
        font-size: 1.5rem;
    }
    
    .form-control {
        padding: 10px 14px;
    }
    
    .btn-login {
        padding: 10px;
        font-size: 1rem;
    }
}

/* Loading Animation */
.btn-loading .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Form Validation Styles */
.was-validated .form-control:invalid {
    border-color: #dc3545;
}

.was-validated .form-control:valid {
    border-color: #28a745;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .login-card {
        background: rgba(33, 37, 41, 0.95);
        color: #fff;
    }
    
    .login-header h2 {
        color: #fff;
    }
    
    .login-header p {
        color: #adb5bd;
    }
    
    .form-label {
        color: #adb5bd;
    }
    
    .form-control {
        background: rgba(255, 255, 255, 0.1);
        border-color: #495057;
        color: #fff;
    }
    
    .form-control:focus {
        background: rgba(255, 255, 255, 0.15);
    }
    
    .password-toggle {
        background: rgba(255, 255, 255, 0.1);
        border-color: #495057;
        color: #fff;
    }
    
    .security-info {
        background: rgba(102, 126, 234, 0.2);
    }
    
    .security-info small {
        color: #adb5bd;
    }
}
