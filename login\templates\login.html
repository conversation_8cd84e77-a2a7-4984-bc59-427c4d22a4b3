<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Secure Access</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/login.css') }}" rel="stylesheet">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="login-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h2>Admin <PERSON></h2>
                <p class="text-muted">Secure access to admin dashboard</p>
            </div>
            
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <div class="flash-messages">
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'info-circle' if category == 'info' else 'check-circle' }}"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
            {% endwith %}
            
            <!-- Lockout Status -->
            {% if lockout_info and lockout_info.is_locked %}
                <div class="alert alert-warning lockout-alert">
                    <i class="fas fa-lock"></i>
                    <strong>Account Temporarily Locked</strong>
                    <div class="lockout-timer">
                        <span id="lockout-countdown">{{ lockout_info.remaining_time }}</span> seconds remaining
                    </div>
                    <small>Too many failed login attempts. Please wait before trying again.</small>
                </div>
            {% endif %}
            
            <form method="POST" class="login-form" id="loginForm">
                <!-- CSRF Token -->
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                
                <div class="form-group">
                    <label for="username" class="form-label">
                        <i class="fas fa-user"></i> Username
                    </label>
                    <input type="text" 
                           class="form-control" 
                           id="username" 
                           name="username" 
                           required 
                           autocomplete="username"
                           {% if lockout_info and lockout_info.is_locked %}disabled{% endif %}
                           placeholder="Enter your username">
                    <div class="invalid-feedback"></div>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock"></i> Password
                    </label>
                    <div class="password-input-group">
                        <input type="password" 
                               class="form-control" 
                               id="password" 
                               name="password" 
                               required 
                               autocomplete="current-password"
                               {% if lockout_info and lockout_info.is_locked %}disabled{% endif %}
                               placeholder="Enter your password">
                        <button type="button" class="btn btn-outline-secondary password-toggle" id="togglePassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="invalid-feedback"></div>
                </div>
                
                <div class="form-group">
                    <button type="submit" 
                            class="btn btn-primary btn-login" 
                            id="loginBtn"
                            {% if lockout_info and lockout_info.is_locked %}disabled{% endif %}>
                        <span class="btn-text">
                            <i class="fas fa-sign-in-alt"></i> Sign In
                        </span>
                        <span class="btn-loading d-none">
                            <i class="fas fa-spinner fa-spin"></i> Signing In...
                        </span>
                    </button>
                </div>
            </form>
            
            <div class="login-footer">
                <div class="security-info">
                    <i class="fas fa-info-circle"></i>
                    <small>
                        This system tracks login attempts for security purposes.
                        After 5 failed attempts, access will be temporarily blocked.
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password toggle functionality
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordField = document.getElementById('password');
            const toggleIcon = this.querySelector('i');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        });
        
        // Form submission with loading state
        document.getElementById('loginForm').addEventListener('submit', function() {
            const submitBtn = document.getElementById('loginBtn');
            const btnText = submitBtn.querySelector('.btn-text');
            const btnLoading = submitBtn.querySelector('.btn-loading');
            
            submitBtn.disabled = true;
            btnText.classList.add('d-none');
            btnLoading.classList.remove('d-none');
        });
        
        // Lockout countdown timer
        {% if lockout_info and lockout_info.is_locked %}
        let remainingTime = {{ lockout_info.remaining_time }};
        const countdownElement = document.getElementById('lockout-countdown');
        
        const countdown = setInterval(function() {
            remainingTime--;
            countdownElement.textContent = remainingTime;
            
            if (remainingTime <= 0) {
                clearInterval(countdown);
                location.reload(); // Refresh page when lockout expires
            }
        }, 1000);
        {% endif %}
        
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert:not(.lockout-alert)');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // Form validation
        (function() {
            'use strict';
            
            const form = document.getElementById('loginForm');
            
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                
                form.classList.add('was-validated');
            }, false);
        })();
    </script>
</body>
</html>
