import re
import requests
import json
from user_agents import parse

def get_device_info(user_agent_string):
    """Extract device information from user agent string"""
    if not user_agent_string:
        return {
            'device_type': 'Unknown',
            'browser_name': 'Unknown',
            'browser_version': 'Unknown',
            'operating_system': 'Unknown'
        }
    
    try:
        # Parse user agent using user_agents library
        user_agent = parse(user_agent_string)
        
        # Determine device type
        if user_agent.is_mobile:
            device_type = 'Mobile'
        elif user_agent.is_tablet:
            device_type = 'Tablet'
        elif user_agent.is_pc:
            device_type = 'Desktop'
        else:
            device_type = 'Unknown'
        
        # Get browser information
        browser_name = user_agent.browser.family or 'Unknown'
        browser_version = user_agent.browser.version_string or 'Unknown'
        
        # Get operating system
        os_name = user_agent.os.family or 'Unknown'
        os_version = user_agent.os.version_string or ''
        operating_system = f"{os_name} {os_version}".strip() if os_version else os_name
        
        return {
            'device_type': device_type,
            'browser_name': browser_name,
            'browser_version': browser_version,
            'operating_system': operating_system
        }
        
    except Exception as e:
        print(f"Error parsing user agent: {e}")
        # Fallback to manual parsing
        return parse_user_agent_manual(user_agent_string)

def parse_user_agent_manual(user_agent_string):
    """Manual user agent parsing as fallback"""
    device_type = 'Desktop'  # Default
    browser_name = 'Unknown'
    browser_version = 'Unknown'
    operating_system = 'Unknown'
    
    try:
        ua = user_agent_string.lower()
        
        # Detect device type
        if any(mobile in ua for mobile in ['mobile', 'android', 'iphone', 'ipod', 'blackberry', 'windows phone']):
            device_type = 'Mobile'
        elif any(tablet in ua for tablet in ['tablet', 'ipad']):
            device_type = 'Tablet'
        
        # Detect browser
        if 'chrome' in ua and 'edge' not in ua:
            browser_name = 'Chrome'
            match = re.search(r'chrome/(\d+\.\d+)', ua)
            if match:
                browser_version = match.group(1)
        elif 'firefox' in ua:
            browser_name = 'Firefox'
            match = re.search(r'firefox/(\d+\.\d+)', ua)
            if match:
                browser_version = match.group(1)
        elif 'safari' in ua and 'chrome' not in ua:
            browser_name = 'Safari'
            match = re.search(r'version/(\d+\.\d+)', ua)
            if match:
                browser_version = match.group(1)
        elif 'edge' in ua:
            browser_name = 'Edge'
            match = re.search(r'edge/(\d+\.\d+)', ua)
            if match:
                browser_version = match.group(1)
        elif 'opera' in ua or 'opr' in ua:
            browser_name = 'Opera'
            match = re.search(r'(?:opera|opr)/(\d+\.\d+)', ua)
            if match:
                browser_version = match.group(1)
        
        # Detect operating system
        if 'windows nt 10' in ua:
            operating_system = 'Windows 10'
        elif 'windows nt 6.3' in ua:
            operating_system = 'Windows 8.1'
        elif 'windows nt 6.2' in ua:
            operating_system = 'Windows 8'
        elif 'windows nt 6.1' in ua:
            operating_system = 'Windows 7'
        elif 'windows' in ua:
            operating_system = 'Windows'
        elif 'mac os x' in ua:
            match = re.search(r'mac os x (\d+[._]\d+)', ua)
            if match:
                version = match.group(1).replace('_', '.')
                operating_system = f'macOS {version}'
            else:
                operating_system = 'macOS'
        elif 'linux' in ua:
            operating_system = 'Linux'
        elif 'android' in ua:
            match = re.search(r'android (\d+\.\d+)', ua)
            if match:
                operating_system = f'Android {match.group(1)}'
            else:
                operating_system = 'Android'
        elif 'iphone os' in ua or 'ios' in ua:
            match = re.search(r'os (\d+[._]\d+)', ua)
            if match:
                version = match.group(1).replace('_', '.')
                operating_system = f'iOS {version}'
            else:
                operating_system = 'iOS'
        
    except Exception as e:
        print(f"Error in manual user agent parsing: {e}")
    
    return {
        'device_type': device_type,
        'browser_name': browser_name,
        'browser_version': browser_version,
        'operating_system': operating_system
    }

def get_location_info(ip_address):
    """Get geographic location information from IP address"""
    # Skip location lookup for local/private IPs
    if is_private_ip(ip_address):
        return {
            'country': 'Local',
            'city': 'Local',
            'region': 'Local',
            'latitude': None,
            'longitude': None
        }
    
    try:
        # Using ipapi.co for geolocation (free tier available)
        response = requests.get(f'https://ipapi.co/{ip_address}/json/', timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            
            return {
                'country': data.get('country_name', 'Unknown'),
                'city': data.get('city', 'Unknown'),
                'region': data.get('region', 'Unknown'),
                'latitude': data.get('latitude'),
                'longitude': data.get('longitude')
            }
        else:
            print(f"Geolocation API error: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"Error fetching location data: {e}")
    except Exception as e:
        print(f"Unexpected error in location lookup: {e}")
    
    # Return default values if lookup fails
    return {
        'country': 'Unknown',
        'city': 'Unknown',
        'region': 'Unknown',
        'latitude': None,
        'longitude': None
    }

def is_private_ip(ip_address):
    """Check if IP address is private/local"""
    try:
        # Common private IP ranges
        private_ranges = [
            '127.',      # Loopback
            '10.',       # Private Class A
            '172.16.',   # Private Class B (simplified check)
            '192.168.',  # Private Class C
            '::1',       # IPv6 loopback
            'fc00:',     # IPv6 private
            'fe80:',     # IPv6 link-local
        ]
        
        return any(ip_address.startswith(range_start) for range_start in private_ranges)
        
    except Exception:
        return True  # Assume private if we can't determine

def format_timestamp(timestamp):
    """Format timestamp for display"""
    if not timestamp:
        return 'Unknown'
    
    try:
        if isinstance(timestamp, str):
            from datetime import datetime
            timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        
        return timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')
    except Exception:
        return str(timestamp)

def get_success_status_display(success):
    """Get display text for success status"""
    return 'Success' if success else 'Failed'

def get_success_status_class(success):
    """Get CSS class for success status"""
    return 'success' if success else 'danger'
