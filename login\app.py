from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify
from datetime import datetime, timedelta
import os
import secrets
from models import db, LoginAttempt, FailedAttemptTracker, init_db
from auth import AuthManager, generate_csrf_token, validate_csrf_token
from utils import get_device_info, get_location_info, format_timestamp

app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', secrets.token_hex(32))
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///login_system.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=2)

# Initialize database
init_db(app)

# Template context processors
@app.context_processor
def inject_csrf_token():
    return dict(csrf_token=generate_csrf_token())

@app.context_processor
def inject_session():
    return dict(session=session)

# Routes
@app.route('/')
def index():
    """Redirect to login if not authenticated, otherwise to dashboard"""
    if AuthManager.is_logged_in():
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Login page with security features"""
    if AuthManager.is_logged_in():
        return redirect(url_for('dashboard'))
    
    # Get lockout information for current IP
    ip_address = AuthManager.get_client_ip()
    lockout_info = {
        'is_locked': AuthManager.is_ip_locked(ip_address),
        'remaining_time': AuthManager.get_lockout_time_remaining(ip_address)
    }
    
    if request.method == 'POST':
        # Validate CSRF token
        csrf_token = request.form.get('csrf_token')
        if not validate_csrf_token(csrf_token):
            flash('Security token invalid. Please try again.', 'error')
            return render_template('login.html', lockout_info=lockout_info)
        
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        
        # Basic validation
        if not username or not password:
            flash('Please enter both username and password.', 'error')
            return render_template('login.html', lockout_info=lockout_info)
        
        # Attempt login
        success, status = AuthManager.login_user(username, password)
        
        if success:
            return redirect(url_for('dashboard'))
        else:
            # Update lockout info after failed attempt
            lockout_info = {
                'is_locked': AuthManager.is_ip_locked(ip_address),
                'remaining_time': AuthManager.get_lockout_time_remaining(ip_address)
            }
    
    return render_template('login.html', lockout_info=lockout_info)

@app.route('/dashboard')
def dashboard():
    """Admin dashboard with login attempt logs"""
    if not AuthManager.is_logged_in():
        flash('Please log in to access the dashboard.', 'error')
        return redirect(url_for('login'))
    
    # Get filter parameters
    status_filter = request.args.get('status', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    ip_filter = request.args.get('ip', '')
    
    # Build query
    query = LoginAttempt.query
    
    # Apply filters
    if status_filter == 'success':
        query = query.filter(LoginAttempt.success == True)
    elif status_filter == 'failed':
        query = query.filter(LoginAttempt.success == False)
    
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(LoginAttempt.timestamp >= date_from_obj)
        except ValueError:
            flash('Invalid from date format.', 'error')
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d') + timedelta(days=1)
            query = query.filter(LoginAttempt.timestamp < date_to_obj)
        except ValueError:
            flash('Invalid to date format.', 'error')
    
    if ip_filter:
        query = query.filter(LoginAttempt.ip_address.contains(ip_filter))
    
    # Get attempts ordered by timestamp (newest first)
    attempts = query.order_by(LoginAttempt.timestamp.desc()).all()
    
    # Calculate statistics
    stats = get_dashboard_stats()
    
    return render_template('dashboard.html', attempts=attempts, stats=stats)

@app.route('/logout')
def logout():
    """Logout user and redirect to login page"""
    AuthManager.logout_user()
    return redirect(url_for('login'))

# API Routes
@app.route('/api/login-attempt/<int:attempt_id>')
def get_login_attempt(attempt_id):
    """Get detailed information about a specific login attempt"""
    if not AuthManager.is_logged_in():
        return jsonify({'success': False, 'message': 'Unauthorized'}), 401
    
    attempt = LoginAttempt.query.get_or_404(attempt_id)
    
    return jsonify({
        'success': True,
        'attempt': attempt.to_dict()
    })

@app.route('/api/dashboard-stats')
def get_dashboard_stats():
    """Get current dashboard statistics"""
    if not AuthManager.is_logged_in():
        return jsonify({'success': False, 'message': 'Unauthorized'}), 401
    
    stats = get_dashboard_stats()
    return jsonify({
        'success': True,
        'stats': stats
    })

# Helper functions
def get_dashboard_stats():
    """Calculate dashboard statistics"""
    total_attempts = LoginAttempt.query.count()
    successful_attempts = LoginAttempt.query.filter(LoginAttempt.success == True).count()
    failed_attempts = LoginAttempt.query.filter(LoginAttempt.success == False).count()
    
    # Count currently locked IPs
    locked_ips = FailedAttemptTracker.query.filter(
        FailedAttemptTracker.locked_until > datetime.utcnow()
    ).count()
    
    return {
        'total_attempts': total_attempts,
        'successful_attempts': successful_attempts,
        'failed_attempts': failed_attempts,
        'locked_ips': locked_ips
    }

# Error handlers
@app.errorhandler(404)
def not_found_error(error):
    return render_template('error.html', 
                         error_code=404, 
                         error_message='Page not found'), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return render_template('error.html', 
                         error_code=500, 
                         error_message='Internal server error'), 500

@app.errorhandler(403)
def forbidden_error(error):
    return render_template('error.html', 
                         error_code=403, 
                         error_message='Access forbidden'), 403

# Security headers
@app.after_request
def after_request(response):
    """Add security headers to all responses"""
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    response.headers['Content-Security-Policy'] = (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://code.jquery.com https://cdn.datatables.net; "
        "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
        "font-src 'self' https://cdnjs.cloudflare.com; "
        "img-src 'self' data:; "
        "connect-src 'self';"
    )
    return response

if __name__ == '__main__':
    # Create tables if they don't exist
    with app.app_context():
        db.create_all()
    
    # Run the application
    app.run(debug=True, host='0.0.0.0', port=5000)
