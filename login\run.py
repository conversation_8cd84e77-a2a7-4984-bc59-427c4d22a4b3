#!/usr/bin/env python3
"""
Secure Login System Startup Script
"""

import os
import sys
from app import app

def main():
    """Main function to run the Flask application"""
    
    print("=" * 60)
    print("🔐 SECURE LOGIN SYSTEM")
    print("=" * 60)
    print()
    print("📋 System Information:")
    print(f"   • Python Version: {sys.version.split()[0]}")
    print(f"   • Flask Environment: {os.environ.get('FLASK_ENV', 'development')}")
    print(f"   • Database: SQLite (login_system.db)")
    print()
    print("🔑 Login Credentials:")
    print("   • Username: admin")
    print("   • Password: Admin@123")
    print()
    print("🌐 Access URLs:")
    print("   • Login Page: http://localhost:5000/login")
    print("   • Dashboard: http://localhost:5000/dashboard")
    print()
    print("🛡️  Security Features:")
    print("   • Account lockout after 5 failed attempts")
    print("   • 5-minute lockout duration")
    print("   • CSRF protection on all forms")
    print("   • Comprehensive login attempt logging")
    print("   • Device and location tracking")
    print()
    print("📊 Dashboard Features:")
    print("   • Real-time login attempt monitoring")
    print("   • Advanced filtering and search")
    print("   • CSV export functionality")
    print("   • Detailed attempt information")
    print()
    print("=" * 60)
    print("🚀 Starting Flask Application...")
    print("   Press Ctrl+C to stop the server")
    print("=" * 60)
    print()
    
    try:
        # Create database tables if they don't exist
        with app.app_context():
            from models import db
            db.create_all()
            print("✅ Database initialized successfully")
        
        # Run the Flask application
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=True,
            use_debugger=True
        )
        
    except KeyboardInterrupt:
        print("\n" + "=" * 60)
        print("🛑 Server stopped by user")
        print("=" * 60)
        sys.exit(0)
        
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        print("\n🔧 Troubleshooting:")
        print("   1. Make sure you're in the 'login' directory")
        print("   2. Check that all dependencies are installed: pip install -r requirements.txt")
        print("   3. Ensure Python 3.8+ is being used")
        print("   4. Check that port 5000 is not already in use")
        sys.exit(1)

if __name__ == '__main__':
    main()
