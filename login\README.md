# Secure Login System

A comprehensive Flask-based login system with advanced security features, login attempt tracking, and admin dashboard.

## Features

### Security Features
- **Hardcoded Credentials**: Username: `admin`, Password: `Admin@123`
- **Account Lockout**: 5 failed attempts trigger a 5-minute lockout
- **CSRF Protection**: All forms protected against Cross-Site Request Forgery
- **Session Security**: Secure session handling with proper cookie settings
- **Input Validation**: Comprehensive validation and sanitization
- **Security Headers**: CSP, XSS protection, and other security headers

### Login Tracking
- **Comprehensive Logging**: Every login attempt is recorded with:
  - Username attempted
  - Timestamp (date and time)
  - Success/failure status
  - User's IP address
  - Device type (desktop, mobile, tablet)
  - Browser name and version
  - Operating system
  - User agent string
  - Geographic location (country, city, region)
  - GPS coordinates (when available)

### Admin Dashboard
- **Real-time Statistics**: Total, successful, failed attempts, and locked IPs
- **Advanced Filtering**: Filter by status, date range, and IP address
- **Data Export**: Export filtered data to CSV format
- **Detailed View**: Click any attempt to see full technical details
- **Responsive Design**: Works perfectly on all devices
- **Auto-refresh**: Dashboard updates every 30 seconds

## Installation

### Prerequisites
- Python 3.8 or higher
- pip (Python package installer)

### Quick Setup

1. **Navigate to the login directory**:
   ```bash
   cd login
   ```

2. **Create a virtual environment** (recommended):
   ```bash
   python -m venv venv
   
   # On Windows:
   venv\Scripts\activate
   
   # On macOS/Linux:
   source venv/bin/activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Run the application**:
   ```bash
   python app.py
   ```

5. **Access the application**:
   - Open your browser and go to: `http://localhost:5000`
   - Login with: Username: `admin`, Password: `Admin@123`

## File Structure

```
login/
├── app.py                 # Main Flask application
├── models.py              # Database models
├── auth.py                # Authentication logic
├── utils.py               # Utility functions
├── config.py              # Configuration settings
├── requirements.txt       # Python dependencies
├── README.md             # This file
├── templates/            # HTML templates
│   ├── login.html        # Login form
│   ├── dashboard.html    # Admin dashboard
│   └── error.html        # Error pages
└── static/               # Static files
    ├── css/
    │   ├── login.css     # Login page styles
    │   └── dashboard.css # Dashboard styles
    └── js/
        └── dashboard.js  # Dashboard JavaScript
```

## Configuration

### Environment Variables

You can customize the application using environment variables:

- `SECRET_KEY`: Flask secret key for session security
- `DATABASE_URL`: Database connection string (default: SQLite)
- `LOG_LEVEL`: Logging level (DEBUG, INFO, WARNING, ERROR)

### Example .env file:
```bash
SECRET_KEY=your-super-secret-key-here
DATABASE_URL=sqlite:///login_system.db
LOG_LEVEL=INFO
```

## Database

The system uses SQLite by default, which requires no additional setup. The database file (`login_system.db`) will be created automatically when you first run the application.

### Database Tables

1. **login_attempts**: Stores all login attempt information
2. **failed_attempts**: Tracks failed attempts for lockout mechanism

## Security Features Explained

### Account Lockout Mechanism
- After 5 failed login attempts from the same IP address
- Account is locked for exactly 5 minutes
- Lockout timer is displayed to the user
- Successful login resets the failed attempt counter

### CSRF Protection
- All forms include CSRF tokens
- Tokens are validated on form submission
- Prevents Cross-Site Request Forgery attacks

### Session Security
- Secure session cookies (HTTPS only in production)
- HttpOnly cookies to prevent XSS
- SameSite attribute for CSRF protection
- 2-hour session timeout

### Data Protection
- User agent strings and IP addresses are logged for security
- Geographic location is determined from IP (using ipapi.co)
- All sensitive operations are logged

## API Endpoints

### Public Endpoints
- `GET /` - Redirects to login or dashboard
- `GET /login` - Login form
- `POST /login` - Process login
- `GET /logout` - Logout user

### Protected Endpoints (require authentication)
- `GET /dashboard` - Admin dashboard
- `GET /api/login-attempt/<id>` - Get attempt details
- `GET /api/dashboard-stats` - Get current statistics

## Customization

### Changing Credentials
Edit the `ADMIN_USERNAME` and `ADMIN_PASSWORD` constants in `auth.py`:

```python
ADMIN_USERNAME = "your_username"
ADMIN_PASSWORD = "your_password"
```

### Adjusting Security Settings
Modify the lockout settings in `config.py`:

```python
MAX_FAILED_ATTEMPTS = 5  # Number of attempts before lockout
LOCKOUT_DURATION_MINUTES = 5  # Lockout duration in minutes
```

### Database Configuration
For production, use a more robust database like PostgreSQL:

```bash
pip install psycopg2-binary
export DATABASE_URL="postgresql://user:password@localhost/login_system"
```

## Production Deployment

### Using Gunicorn (Linux/macOS)
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### Using Waitress (Windows)
```bash
pip install waitress
waitress-serve --host=0.0.0.0 --port=5000 app:app
```

### Environment Variables for Production
```bash
export FLASK_ENV=production
export SECRET_KEY=your-production-secret-key
export DATABASE_URL=your-production-database-url
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure you're in the `login` directory and have activated your virtual environment

2. **Database Errors**: Delete `login_system.db` and restart the application to recreate the database

3. **Permission Errors**: Ensure the application has write permissions in the directory

4. **Geolocation Not Working**: The system will continue to work without geolocation data if the API is unavailable

### Logs
Check the application logs for detailed error information. Logs are printed to the console in development mode.

## License

This project is provided as-is for educational and development purposes.

## Support

For issues or questions, please check the code comments and this documentation first. The system is designed to be self-contained and easy to understand.
