// Dashboard JavaScript
$(document).ready(function() {
    // Initialize DataTable
    const table = $('#loginAttemptsTable').DataTable({
        responsive: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
        order: [[0, 'desc']], // Sort by timestamp descending
        columnDefs: [
            {
                targets: [0], // Timestamp column
                type: 'date'
            },
            {
                targets: [8], // Actions column
                orderable: false,
                searchable: false
            }
        ],
        language: {
            search: "Search records:",
            lengthMenu: "Show _MENU_ records per page",
            info: "Showing _START_ to _END_ of _TOTAL_ records",
            infoEmpty: "No records available",
            infoFiltered: "(filtered from _MAX_ total records)",
            paginate: {
                first: "First",
                last: "Last",
                next: "Next",
                previous: "Previous"
            }
        },
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
             '<"row"<"col-sm-12"tr>>' +
             '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
        initComplete: function() {
            // Add fade-in animation to table
            $('#loginAttemptsTable').addClass('fade-in');
        }
    });

    // Auto-refresh data every 30 seconds
    setInterval(function() {
        refreshData();
    }, 30000);

    // Add animation classes to cards
    $('.card').addClass('slide-up');
    $('.stat-card').each(function(index) {
        $(this).css('animation-delay', (index * 0.1) + 's');
    });
});

// Show login attempt details in modal
function showDetails(attemptId) {
    const modal = new bootstrap.Modal(document.getElementById('detailsModal'));
    const modalBody = document.getElementById('modalBody');
    
    // Show loading state
    modalBody.innerHTML = `
        <div class="text-center p-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading details...</p>
        </div>
    `;
    
    modal.show();
    
    // Fetch attempt details
    fetch(`/api/login-attempt/${attemptId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayAttemptDetails(data.attempt);
            } else {
                modalBody.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        Error loading details: ${data.message}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error fetching details:', error);
            modalBody.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    Error loading details. Please try again.
                </div>
            `;
        });
}

// Display attempt details in modal
function displayAttemptDetails(attempt) {
    const modalBody = document.getElementById('modalBody');
    
    const statusBadge = attempt.success 
        ? '<span class="badge bg-success"><i class="fas fa-check"></i> Success</span>'
        : '<span class="badge bg-danger"><i class="fas fa-times"></i> Failed</span>';
    
    const location = attempt.location_city && attempt.location_country
        ? `${attempt.location_city}, ${attempt.location_region || ''} ${attempt.location_country}`.replace(', ,', ',')
        : attempt.location_country || 'Unknown';
    
    const coordinates = attempt.latitude && attempt.longitude
        ? `${attempt.latitude}, ${attempt.longitude}`
        : 'Not available';
    
    modalBody.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-info-circle text-primary"></i> Basic Information</h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>Username:</strong></td>
                        <td>${attempt.username}</td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>${statusBadge}</td>
                    </tr>
                    <tr>
                        <td><strong>Timestamp:</strong></td>
                        <td>${formatTimestamp(attempt.timestamp)}</td>
                    </tr>
                    <tr>
                        <td><strong>IP Address:</strong></td>
                        <td><code>${attempt.ip_address}</code></td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-map-marker-alt text-success"></i> Location Information</h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>Location:</strong></td>
                        <td>${location}</td>
                    </tr>
                    <tr>
                        <td><strong>Coordinates:</strong></td>
                        <td>${coordinates}</td>
                    </tr>
                </table>
                
                <h6><i class="fas fa-desktop text-info"></i> Device Information</h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>Device Type:</strong></td>
                        <td>${attempt.device_type || 'Unknown'}</td>
                    </tr>
                    <tr>
                        <td><strong>Operating System:</strong></td>
                        <td>${attempt.operating_system || 'Unknown'}</td>
                    </tr>
                    <tr>
                        <td><strong>Browser:</strong></td>
                        <td>${attempt.browser_name || 'Unknown'} ${attempt.browser_version || ''}</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-12">
                <h6><i class="fas fa-code text-warning"></i> Technical Details</h6>
                <div class="bg-light p-3 rounded">
                    <strong>User Agent:</strong><br>
                    <code style="word-break: break-all; font-size: 0.8rem;">
                        ${attempt.user_agent || 'Not available'}
                    </code>
                </div>
            </div>
        </div>
        
        ${coordinates !== 'Not available' ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6><i class="fas fa-globe text-primary"></i> Map Location</h6>
                <div class="text-center">
                    <a href="https://www.google.com/maps?q=${attempt.latitude},${attempt.longitude}" 
                       target="_blank" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-external-link-alt"></i> View on Google Maps
                    </a>
                </div>
            </div>
        </div>
        ` : ''}
    `;
}

// Format timestamp for display
function formatTimestamp(timestamp) {
    if (!timestamp) return 'Unknown';
    
    try {
        const date = new Date(timestamp);
        return date.toLocaleString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            timeZoneName: 'short'
        });
    } catch (error) {
        return timestamp;
    }
}

// Export data to CSV
function exportData() {
    const table = $('#loginAttemptsTable').DataTable();
    const data = table.rows({search: 'applied'}).data().toArray();
    
    if (data.length === 0) {
        alert('No data to export');
        return;
    }
    
    // Show loading state
    const exportBtn = event.target;
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Exporting...';
    exportBtn.disabled = true;
    
    // Prepare CSV data
    const headers = [
        'Timestamp', 'Username', 'Status', 'IP Address', 'Location', 
        'Device Type', 'Browser', 'Operating System', 'User Agent'
    ];
    
    let csvContent = headers.join(',') + '\n';
    
    // Add data rows
    data.forEach(row => {
        const csvRow = [
            `"${$(row[0]).text() || ''}"`, // Timestamp
            `"${$(row[1]).text() || ''}"`, // Username
            `"${$(row[2]).text().replace(/\s+/g, ' ').trim() || ''}"`, // Status
            `"${$(row[3]).text() || ''}"`, // IP Address
            `"${$(row[4]).text() || ''}"`, // Location
            `"${$(row[5]).text() || ''}"`, // Device Type
            `"${$(row[6]).text() || ''}"`, // Browser
            `"${$(row[7]).text() || ''}"`, // Operating System
            `"${$(row[8]).attr('data-user-agent') || ''}"` // User Agent (from data attribute)
        ];
        csvContent += csvRow.join(',') + '\n';
    });
    
    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `login_attempts_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Reset button
    setTimeout(() => {
        exportBtn.innerHTML = originalText;
        exportBtn.disabled = false;
    }, 1000);
}

// Refresh dashboard data
function refreshData() {
    // Only refresh if no modal is open
    if (!document.querySelector('.modal.show')) {
        fetch('/api/dashboard-stats')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateStatistics(data.stats);
                }
            })
            .catch(error => {
                console.error('Error refreshing data:', error);
            });
    }
}

// Update statistics cards
function updateStatistics(stats) {
    const cards = document.querySelectorAll('.stat-card h3');
    const newValues = [
        stats.total_attempts,
        stats.successful_attempts,
        stats.failed_attempts,
        stats.locked_ips
    ];
    
    cards.forEach((card, index) => {
        const currentValue = parseInt(card.textContent);
        const newValue = newValues[index];
        
        if (currentValue !== newValue) {
            // Animate value change
            card.style.transform = 'scale(1.1)';
            card.style.color = '#28a745';
            
            setTimeout(() => {
                card.textContent = newValue;
                setTimeout(() => {
                    card.style.transform = 'scale(1)';
                    card.style.color = '';
                }, 150);
            }, 150);
        }
    });
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
