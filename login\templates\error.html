<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error {{ error_code }} - Login System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .error-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        
        .error-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 20px;
        }
        
        .error-code {
            font-size: 3rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
        }
        
        .error-message {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 30px;
        }
        
        .btn-home {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 12px 30px;
            font-weight: 600;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
        
        @media (max-width: 480px) {
            .error-container {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .error-icon {
                font-size: 3rem;
            }
            
            .error-code {
                font-size: 2.5rem;
            }
            
            .error-message {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            {% if error_code == 404 %}
                <i class="fas fa-search"></i>
            {% elif error_code == 403 %}
                <i class="fas fa-lock"></i>
            {% elif error_code == 500 %}
                <i class="fas fa-exclamation-triangle"></i>
            {% else %}
                <i class="fas fa-exclamation-circle"></i>
            {% endif %}
        </div>
        
        <div class="error-code">{{ error_code }}</div>
        
        <div class="error-message">
            {{ error_message }}
        </div>
        
        <div class="error-description">
            {% if error_code == 404 %}
                <p>The page you're looking for doesn't exist or has been moved.</p>
            {% elif error_code == 403 %}
                <p>You don't have permission to access this resource.</p>
            {% elif error_code == 500 %}
                <p>Something went wrong on our end. Please try again later.</p>
            {% else %}
                <p>An unexpected error occurred.</p>
            {% endif %}
        </div>
        
        <div class="mt-4">
            <a href="{{ url_for('index') }}" class="btn-home">
                <i class="fas fa-home"></i> Go Home
            </a>
        </div>
    </div>
</body>
</html>
