from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, timedelta
import json

db = SQLAlchemy()

class LoginAttempt(db.Model):
    """Model to store login attempt information"""
    __tablename__ = 'login_attempts'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(100), nullable=False, index=True)
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, index=True)
    success = db.Column(db.<PERSON>, nullable=False, index=True)
    ip_address = db.Column(db.String(45), nullable=False, index=True)  # IPv6 support
    device_type = db.Column(db.String(20), nullable=True)  # desktop, mobile, tablet
    browser_name = db.Column(db.String(50), nullable=True)
    browser_version = db.Column(db.String(20), nullable=True)
    operating_system = db.Column(db.String(50), nullable=True)
    user_agent = db.Column(db.Text, nullable=True)
    location_country = db.Column(db.String(100), nullable=True)
    location_city = db.Column(db.String(100), nullable=True)
    location_region = db.Column(db.String(100), nullable=True)
    latitude = db.Column(db.Float, nullable=True)
    longitude = db.Column(db.Float, nullable=True)
    
    def __repr__(self):
        return f'<LoginAttempt {self.username} at {self.timestamp}>'
    
    def to_dict(self):
        """Convert model to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'username': self.username,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'success': self.success,
            'ip_address': self.ip_address,
            'device_type': self.device_type,
            'browser_name': self.browser_name,
            'browser_version': self.browser_version,
            'operating_system': self.operating_system,
            'user_agent': self.user_agent,
            'location_country': self.location_country,
            'location_city': self.location_city,
            'location_region': self.location_region,
            'latitude': self.latitude,
            'longitude': self.longitude
        }

class FailedAttemptTracker(db.Model):
    """Model to track failed login attempts for lockout mechanism"""
    __tablename__ = 'failed_attempts'
    
    id = db.Column(db.Integer, primary_key=True)
    ip_address = db.Column(db.String(45), nullable=False, unique=True, index=True)
    failed_count = db.Column(db.Integer, nullable=False, default=0)
    last_attempt = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    locked_until = db.Column(db.DateTime, nullable=True)
    
    def __repr__(self):
        return f'<FailedAttemptTracker {self.ip_address}: {self.failed_count} attempts>'
    
    def is_locked(self):
        """Check if IP is currently locked"""
        if self.locked_until and datetime.utcnow() < self.locked_until:
            return True
        return False
    
    def get_remaining_lockout_time(self):
        """Get remaining lockout time in seconds"""
        if self.is_locked():
            return int((self.locked_until - datetime.utcnow()).total_seconds())
        return 0
    
    def increment_failed_attempts(self):
        """Increment failed attempts and lock if necessary"""
        self.failed_count += 1
        self.last_attempt = datetime.utcnow()
        
        if self.failed_count >= 5:
            self.locked_until = datetime.utcnow() + timedelta(minutes=5)
    
    def reset_attempts(self):
        """Reset failed attempts after successful login"""
        self.failed_count = 0
        self.locked_until = None
        self.last_attempt = datetime.utcnow()

def init_db(app):
    """Initialize database with Flask app"""
    db.init_app(app)
    
    with app.app_context():
        # Create all tables
        db.create_all()
        
        # Create indexes for better performance
        try:
            db.engine.execute('CREATE INDEX IF NOT EXISTS idx_login_attempts_timestamp_success ON login_attempts(timestamp, success)')
            db.engine.execute('CREATE INDEX IF NOT EXISTS idx_login_attempts_ip_timestamp ON login_attempts(ip_address, timestamp)')
        except Exception as e:
            print(f"Warning: Could not create additional indexes: {e}")
